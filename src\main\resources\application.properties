app.id=customer-admin-web
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=global.properties,application.properties

# Spring应用配置
spring.application.name=customer-admin-web
server.port=8244


# 日志配置
logging.level.root=INFO
logging.level.com.github.ebnew.ki4so=DEBUG
logging.level.org.springframework.web=ERROR
logging.level.com.tem.platform=warn
logging.level.org.hibernate=ERROR
logging.level.org.apache.dubbo=INFO

# Dubbo配置
dubbo.application.name=customer-admin-web
dubbo.application.owner=ex

# ZooKeeper注册中心配置
dubbo.registry.protocol=zookeeper
dubbo.registry.address=${zookeeper.cluster}
dubbo.registry.client=curator
dubbo.registry.file=./.dubbo-customer-admin-web
dubbo.registry.timeout=60000
dubbo.registry.parameters.blockUntilConnectedWait=30
dubbo.registry.parameters.init.timeout=60000
dubbo.registry.parameters.timeout=60000


dubbo.config-center.timeout=20000

# 消费者配置
dubbo.consumer.timeout=10000
dubbo.consumer.provided-by= tem-service
dubbo.consumer.check=false

# 提供者配置
dubbo.provider.timeout=10000

# Dubbo协议配置
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=21886
dubbo.protocol.payload=83886080


# 数据库配置
spring.datasource.driver-class-name=${jdbc.driver}
spring.datasource.url=${jdbc.url}
spring.datasource.username=${jdbc.username}

# 数据库密码加密配置
# 公钥配置（用于密码解密）
jdbc.publickey=${jdbc.publickey}
# 加密后的密码
jdbc.encrypt.password=${jdbc.encrypt.password}

# 七鱼CRM配置
qiyu.crm.app.key=${qiyu.crm.app.key}
qiyu.crm.app.secret=${qiyu.crm.app.secret}
qiyu.crm.token.timeout=${qiyu.crm.token.timeout:120}
qiyu.crm.auth.time.tolerance=${qiyu.crm.auth.time.tolerance:300}

# 企业微信API配置
wechat.api.enabled=true
wechat.api.base-url=${wechat.api.base.url:https://qyapi.weixin.qq.com}
wechat.api.corp-id=${wechat.api.corp.id}
wechat.api.corp-secret=${wechat.api.corp.secret}
wechat.api.agent-id=${wechat.api.agent.id}
wechat.api.connect-timeout=${wechat.api.connect.timeout:10000}
wechat.api.read-timeout=${wechat.api.read.timeout:30000}
wechat.api.retry-count=${wechat.api.retry.count:3}
wechat.api.token-cache-time=${wechat.api.token.cache.time:7000}

# Druid连接池配置
spring.datasource.druid.initial-size=10
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20



# Sa-Token配置
# Token名称 (同时也是cookie名称)
sa-token.token-name=customer-token
# Token有效期，单位s 默认30天, -1代表永不过期
sa-token.timeout=2592000
# 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
sa-token.is-concurrent=false
# 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
sa-token.is-share=true
# Token风格
sa-token.token-style=uuid
# 是否输出操作日志
sa-token.is-log=false
# 是否从header中读取token
sa-token.is-read-header=true
# 是否从body中读取token
# token前缀
sa-token.token-prefix=Bearer
# JWT秘钥
sa-token.jwt-secret-key=abcdefghijklmnopqrstuvwxyz
# 是否尝试从请求体里读取token
sa-token.is-read-body=false
# 是否尝试从cookie里读取token
sa-token.is-read-cookie=false
# 是否在登录后将Token写入到响应头
sa-token.is-write-header=false

# Spring Session配置
spring.session.store-type=redis
spring.session.redis.flush-mode=on_save
spring.session.redis.cleanup-cron=0 * * * * *
# 配置Spring Session使用特定的Redis连接工厂
spring.session.redis.configure-action=none

# 文件上传配置
# 单个文件最大大小（10MB）
spring.servlet.multipart.max-file-size=10MB
# 请求最大大小（50MB，支持批量上传）
spring.servlet.multipart.max-request-size=50MB
# 文件写入磁盘的阈值（1MB）
spring.servlet.multipart.file-size-threshold=1MB


